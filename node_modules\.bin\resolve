#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -x "$basedir/node" ]; then
  "$basedir/node"  "$basedir/../@babel/helper-define-polyfill-provider/node_modules/resolve/bin/resolve" "$@"
  ret=$?
else 
  node  "$basedir/../@babel/helper-define-polyfill-provider/node_modules/resolve/bin/resolve" "$@"
  ret=$?
fi
exit $ret
