#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -x "$basedir/node" ]; then
  "$basedir/node"  "$basedir/../@babel/helper-compilation-targets/node_modules/update-browserslist-db/cli.js" "$@"
  ret=$?
else 
  node  "$basedir/../@babel/helper-compilation-targets/node_modules/update-browserslist-db/cli.js" "$@"
  ret=$?
fi
exit $ret
