import React, { useState } from 'react';
import { View, Image, StyleSheet, Text, TouchableNativeFeedback, TouchableWithoutFeedback, TouchableOpacity, Pressable } from 'react-native';
import { heightPixel, fonts, widthPixel, getVerticalPadding } from '../../styles';
import { Dimensions } from 'react-native';
import Ripple from 'react-native-material-ripple';
import Svg, { Circle, Defs, LinearGradient, Path, Rect, Stop } from 'react-native-svg';
import Animated, { Extrapolate, Extrapolation, interpolate, useAnimatedStyle } from 'react-native-reanimated';
import ProductModal from './ProductModal';
import { useProductWishList, useUser } from '@appmaker-xyz/shopify';
import { getIdFromGID, updateWebWishlist } from '../../utils/Helper';
import Icon from 'react-native-vector-icons/AntDesign';
import { eventHandler, eventList } from '../../utils/AnalyticsHelper';



const { width: screenWidth } = Dimensions.get('window');
const CARD_WIDTH = screenWidth

const NewDropCard = ({ item, index, scrollX, props, svgColor }) => {

    const [isAddToCartModalVisible, setIsAddToCartModalVisible] = useState(false);
    const imageMedia = item?.node?.media?.edges?.filter(
        (edge) => edge?.node?.mediaContentType === 'IMAGE'
    );

    const mainImg = imageMedia?.[4]?.node?.image?.url

    const color_variant = item?.node?.color_variant?.references?.nodes
    const { user } = useUser();
    const customerId = getIdFromGID(user?.id)

    const updatedProps = {
        ...props,
        blockData: {
            node: item?.node,
        }
    };

    const { toggleWishList, isSaved } = useProductWishList(updatedProps);


    const updateWishlist = () => {
        toggleWishList();
        if (!customerId) return
        const productId = getIdFromGID(item?.node?.id);
        const variantId = getIdFromGID(item?.node?.variants?.edges[0]?.node?.id);
        const action = isSaved ? "remove" : "add";
        eventHandler(isSaved ? eventList.REMOVE_FROM_WHISHLIST : eventList.ADD_TO_WHISHLIST, item?.node);
        updateWebWishlist(customerId, productId, variantId, action)
    }


    const toggleModal = () => {
        setIsAddToCartModalVisible(!isAddToCartModalVisible);
    };


    const BagWithAddIcon = (props) => {
        return (
            <Svg
                width={25}
                height={27}
                viewBox="0 0 25 27"
                fill="none"
            >
                <Path
                    d="M9.60685 0.0576172C12.1019 0.0576172 14.1433 2.0722 14.1433 4.52928V6.37194L17.6717 6.37177C18.1002 6.37177 18.4277 6.69111 18.4277 7.10884V18.6563C18.4277 20.6956 16.7392 22.3416 14.6474 22.3416H4.56658C2.47473 22.3416 0.78623 20.6956 0.78623 18.6563V7.10884C0.78623 6.69111 1.1138 6.37177 1.5423 6.37177H5.07051V4.52911C5.07051 2.07229 7.11191 0.0578738 9.60694 0.0578738L9.60685 0.0576172ZM12.6311 4.52886C12.6311 2.8828 11.2702 1.53141 9.60685 1.53141C7.9435 1.53141 6.58257 2.88264 6.58257 4.52886V6.37152H12.6311V4.52886ZM2.29803 7.87032V18.6807C2.29803 19.9092 3.30606 20.8919 4.56624 20.8919L14.6475 20.8918C15.9076 20.8918 16.9157 19.9091 16.9157 18.6806V7.87016H14.1434V9.14768C14.1434 9.56541 13.8158 9.88474 13.3873 9.88474C12.9588 9.88474 12.6312 9.56541 12.6312 9.14768V7.87016H6.58266V9.14768C6.58266 9.56541 6.25508 9.88474 5.82659 9.88474C5.39809 9.88474 5.07051 9.56541 5.07051 9.14768V7.87016L2.29803 7.87032Z"
                    fill="white"
                    stroke="white"
                    strokeWidth={0.120705}
                />
                <Circle
                    cx={17.5003}
                    cy={18.6292}
                    r={7.428}
                    fill="url(#paint0_linear_58_5829)"
                />
                <Path
                    d="M17.4994 14.9143C17.0436 14.9143 16.6741 15.2838 16.6741 15.7396V17.803H14.6107C14.1549 17.803 13.7854 18.1725 13.7854 18.6283C13.7854 19.0841 14.1549 19.4536 14.6107 19.4536H16.6741V21.517C16.6741 21.9728 17.0436 22.3423 17.4994 22.3423C17.9552 22.3423 18.3247 21.9728 18.3247 21.517V19.4536H20.3881C20.8439 19.4536 21.2134 19.0841 21.2134 18.6283C21.2134 18.1725 20.8439 17.803 20.3881 17.803H18.3247V15.7396C18.3247 15.2838 17.9552 14.9143 17.4994 14.9143Z"
                    fill="#383637"
                    stroke="white"
                    strokeWidth={0.24141}
                />
                <Defs>
                    <LinearGradient
                        id="paint0_linear_58_5829"
                        x1={6.35826}
                        y1={19.5577}
                        x2={21.6785}
                        y2={24.6644}
                        gradientUnits="userSpaceOnUse"
                    >
                        <Stop stopColor="white" />
                        <Stop offset={1} stopColor="white" />
                    </LinearGradient>
                </Defs>
            </Svg>
        );
    };


    return (

        // <Animated.View style={[styles.card, rnAnimatedStyle]}>

        <TouchableOpacity activeOpacity={1} onPress={() => {

            props.onAction({
                action: 'OPEN_PRODUCT',
                params: {
                    productHandle: `${item?.node?.handle}`,
                    replacePage: false,
                },
            });

        }} style={styles.card} >

            <>


                {
                    isAddToCartModalVisible &&
                    <ProductModal
                        modalVisible={isAddToCartModalVisible}
                        toggleModal={toggleModal}
                        handle={item?.node?.handle}
                        productVariantDetails={item?.node?.color_variant?.references}
                        props={props}
                    // addToCart={addToCart}
                    />
                }

                <View>
                    <Image
                        style={styles.image}
                        source={{ uri: mainImg }}
                        resizeMode="cover"
                    />


                    <Ripple
                        onPress={() => {
                            setIsAddToCartModalVisible(true)
                        }}
                        style={{
                            position: "absolute",
                            zIndex: 100,
                            bottom: 0,
                            right: 0,
                            width: widthPixel(56),
                            height: widthPixel(56),
                            backgroundColor: "#302E2F",
                            // padding: widthPixel(15),
                            borderRadius: widthPixel(16),
                            justifyContent: "center",
                            alignItems: "center",
                        }} >

                        <BagWithAddIcon />

                    </Ripple>

                    <View style={{
                        position: 'absolute',
                        right: 0,
                        bottom: 0,
                        width: widthPixel(80),
                        height: heightPixel(85),
                        zIndex: 1,
                        justifyContent: "center",
                        alignItems: "center",
                    }}>
                        <Svg
                            width="100%"
                            height="100%"
                            viewBox="0 0 90 90"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <Path
                                fillRule="evenodd"
                                clipRule="evenodd"
                                d="M93.4744 -203.828C93.8163 -202.604 93.999 -201.312 93.999 -199.979V1.62181C93.999 11.69 78.6586 18.6245 68.6771 17.3062L59.6458 16.1135C32.8568 12.5756 10.3362 36.0615 14.9945 62.6785L17.6327 77.7523C18.962 85.348 13.971 94.7601 6.25996 94.7601H-183.714C-184.42 94.7601 -185.114 94.7089 -185.793 94.6098C-192.698 93.6012 -198 87.6551 -198 80.4706V-199.976C-198 -207.868 -191.602 -214.266 -183.71 -214.266H79.71C85.6283 -214.266 90.7064 -210.668 92.8763 -205.54C90.7073 -210.668 85.6296 -214.266 79.7115 -214.266H-183.714C-191.604 -214.266 -198.001 -207.869 -198.001 -199.979V80.4727C-198.001 87.657 -192.698 93.6028 -185.793 94.6098C-185.113 94.7092 -184.418 94.7606 -183.71 94.7606H79.71C87.6021 94.7606 94 88.3628 94 80.4706V-199.976C94 -201.311 93.8167 -202.603 93.4744 -203.828ZM93.7274 94.5115C93.8179 94.4004 93.999 94.4649 93.999 94.6081C93.999 94.6919 93.9304 94.7601 93.8466 94.7601C93.7185 94.7601 93.6466 94.6108 93.7274 94.5115Z"
                                fill={svgColor ? svgColor : "rgba(251, 249, 237, 1)"}
                            />
                        </Svg>
                    </View>

                    <Pressable

                        style={{
                            position: 'absolute',
                            bottom: 12,
                            left: 5,
                            justifyContent: 'center',
                            alignItems: 'center',
                        }}
                        onPress={() =>
                            setIsAddToCartModalVisible(true)
                        }
                        activeOpacity={0.7}>

                        <Text style={{
                            color: 'white',
                            zIndex: 1,
                            position: 'absolute',
                            fontSize: fonts._9,
                            fontFamily: fonts.FONT_FAMILY.Regular,
                            textAlign: 'center',
                        }}>

                            {color_variant.length}
                        </Text>

                        <Svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="28"
                            height="20"
                            viewBox="0 0 48 37"
                            fill="none">
                            <Rect
                                x="28.98"
                                y="5.774"
                                width="18.281"
                                height="25.313"
                                rx="3.516"
                                fill="#C46B4F"
                                stroke="#fff"
                                strokeWidth="1.406"
                            />
                            <Rect
                                x="0.855"
                                y="5.774"
                                width="16.875"
                                height="25.313"
                                rx="3.516"
                                fill="#557CA6"
                                stroke="#fff"
                                strokeWidth="1.406"
                            />
                            <Rect
                                x="10.699"
                                y="1.556"
                                width="26.719"
                                height="33.75"
                                rx="6.328"
                                fill="#464F51"
                                stroke="#fff"
                                strokeWidth="1.406"
                            />
                        </Svg>
                    </Pressable>

                    <Pressable
                        style={

                            {
                                position: 'absolute',
                                top: 6,
                                right: 8,
                                width: widthPixel(30), // Size to create the circle
                                height: widthPixel(30),
                                borderRadius: widthPixel(15),
                                justifyContent: 'center',
                                alignItems: 'center',
                                backgroundColor: isSaved
                                    ? 'rgba(0, 0, 0, 0.11)'
                                    : 'transparent',
                            }
                        }
                        onPress={updateWishlist}>
                        <Icon
                            style={{ marginTop: 3 }}
                            name={isSaved ? 'heart' : 'hearto'}
                            size={widthPixel(17)}
                            color={isSaved ? 'white' : 'white'}
                        />
                    </Pressable>

                </View>

                <View style={{ width: widthPixel(280), padding: widthPixel(5), maxHeight: heightPixel(125) }} >

                    <Text style={{ fontSize: fonts._14, fontWeight: "500", fontFamily: "Jost" }} >{item?.node?.title}</Text>

                    <Text style={{ fontSize: fonts._20, fontWeight: "700", fontFamily: "Jost" }} >₹{item?.node?.priceRange?.minVariantPrice?.amount}</Text>

                </View>


            </>
            {/* </Animated.View> */}
        </TouchableOpacity>

    );
};

export default NewDropCard;

const styles = StyleSheet.create({
    card: {
        width: widthPixel(280), justifyContent: "center", alignItems: "center", overflow: "hidden",
    },

    image: {
        height: heightPixel(376),
        width: widthPixel(280),
        borderRadius: widthPixel(16),
        // borderTopRightRadius: widthPixel(16),
    },
});
